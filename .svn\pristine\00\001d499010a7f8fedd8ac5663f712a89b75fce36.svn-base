<template>
    <div class="device-container">
        <div class="device-item device-top">
            <div class="item-left" @click="handleClick('all')">
                <div class="device-info">
                    <div class="device-title">接入数量</div>
                    <div class="device-num">{{ deviceInfo.accessNumber || 0}}</div>
                </div>
                <div class="device-img">
                    <img src="@/assets/images/frame-green.png" alt="接入数量" class="img-bg">
                </div>
            </div>
            <div class="item-right">
                <div class="device-info">
                    <div class="device-title">未接入数量</div>
                    <div class="device-num">{{ deviceInfo.NotAccessNumber || 0}}</div>
                </div>
                <div class="device-img">
                    <img src="@/assets/images/frame-orange.png" alt="未接入数量" class="img-bg">
                </div>
            </div>
        </div>
        <div class="device-item device-bt">
            <div class="item-left" @click="handleClick('in')">
                <div class="device-info">
                    <div class="device-title">在线数量</div>
                    <div class="device-num">{{ deviceInfo.onlineNumber || 0}}</div>
                </div>
                <div class="device-img">
                    <img src="@/assets/images/frame-blue.png" alt="工作数量" class="img-bg">
                </div>
            </div>
            <div class="item-right" @click="handleClick('out')">
                <div class="device-info">
                    <div class="device-title">离线数量</div>
                    <div class="device-num">{{ deviceInfo.offlineNumber || 0}}</div>
                </div>
                <div class="device-img">
                    <img src="@/assets/images/frame-purple.png" alt="离线数量" class="img-bg">
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PerceptionDevice',
    props: [],
    data() {
        return {
            deviceInfo: {},
        }
    },
    watch: {
        "$store.state.tree.sectionId": {
            handler: function () {
                this.getPerceptionDevice();
            },
            deep: true,
            immediate: true,
        },
    },
    mounted() {
        
    },

    methods: {
        async getPerceptionDevice(){
            let res = await this.$http.get(`/equip/build/senseDevice?sectionId=${this.$store.state.tree.sectionId}`);
            this.deviceInfo = res?.result || {}
        },

        handleClick(val) {
            this.$router.push({ path: '/devices/access', query: { type: val } });
        },
    }
}
</script>

<style lang="scss" scoped>
.device-container {
    width: 350px;
    height: 200px;

    .device-item {
        width: 100%;
        height: 50%;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;

        .item-left {
            flex: 1;
            padding: 10px;
            border-right: 6px solid #22C5E3;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
        }

        .item-right {
            padding: 10px;
            flex: 1;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
        }

        .device-info {
            flex: 1;

            .device-title {
                width: 100%;
                height: 50%;
                line-height: 37px;
                text-align: left;
                font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
                font-weight: 700;
                font-style: normal;
                font-size: 16px;
                color: #3A3F5D;
            }

            .device-num {
                width: 100%;
                height: 50%;
                line-height: 37px;
                font-family: '微软雅黑', sans-serif;
                font-weight: 400;
                font-style: normal;
                font-size: 24px;
                color: #3D7AF0;
                text-align: left;
            }
        }

        .device-img {
            width: 70px;
            height: 100%;

            .img-bg {
                width: 100%;
                height: 100%;
                background-size: contain;
            }
        }
    }

    .device-top {
        border-bottom: 6px solid #22C5E3;

        .item-left {
            cursor: pointer;
        }
    }

    .device-bt {
        .item-left {
            cursor: pointer;
        }

        .item-right {
            cursor: pointer;
        }
    }
}
</style>