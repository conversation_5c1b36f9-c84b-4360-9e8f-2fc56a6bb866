<template>
    <div class="overview-wrapper">
        <div class="overview-img">
            <img src="@/assets/images/zhzaiji.jpg" alt="充电装载机" class="img-bg"
                v-if="this.deviceInfo.deviceType == '2' || this.deviceInfo.deviceType =='3'">
            <img src="@/assets/images/zixieche.jpg" alt="自卸车" class="img-bg"
                v-else-if="this.deviceInfo.deviceType == '4' || this.deviceInfo.deviceType =='5'">
            <img src="@/assets/images/hunningtu.jpg" alt="混凝土罐车" class="img-bg"
                v-else-if="this.deviceInfo.deviceType == '6' || this.deviceInfo.deviceType =='7'">
            <img src="@/assets/images/waji.png" alt="充电挖机" class="img-bg" v-else>
        </div>
        <div class="overview-info">
            <div class="info-item">
                <span>设备名称:</span>
                <span class="item" v-if="this.deviceInfo.deviceType == '1'">换电挖掘机</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '2'">充电装载机</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '3'">换电装载机</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '4'">充电自卸车</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '5'">换电自卸车</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '6'">充电混泥土车</span>
                <span class="item" v-else-if="this.deviceInfo.deviceType == '7'">换电混泥土车</span>
                <span class="item" v-else>充电挖掘机</span>
            </div>
            <div class="info-item">
                <span>设备编号:</span>
                <span class="item">{{ this.deviceInfo.deviceNum || 'XUG009581J' }}</span>
            </div>
            <div class="info-item">
                <span>车牌号码:</span>
                <span class="item">{{ this.deviceInfo.carNum || '川ADD1235' }}</span>
            </div>
            <div class="info-item">
                <span>管理编号:</span>
                <span class="item">{{ this.deviceInfo.managementNumber || 'ZZJ-002-EFB' }}</span>
            </div>
            <div class="info-item">
                <span>设备规格:</span>
                <span class="item">{{ this.deviceInfo.specificationModel || 'XC968-EV' }}</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'MechanicalOverview',
    props: ['deviceInfo'],
    data() {
        return {
        }
    },
    mounted() {
    },

    methods: {

    }
}
</script>

<style lang="scss" scoped>
.overview-wrapper {
    width: 100%;
    height: 100%;
    // padding: 15px 10px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .overview-img {
        width: 50%;
        height: 100%;

        .img-bg {
            width: 100%;
            height: 100%;
            background-size: contain;
        }
    }

    .overview-info {
        flex: 1;
        margin-left: 15px;
        height: 100%;
        // width: 48%;

        .info-item {
            width: 100%;
            height: 20%;
            line-height: 200%;
            font-family: '微软雅黑', sans-serif;
            font-weight: 400;
            font-style: normal;
            font-size: 16px;
            overflow: hidden; //（文字长度超出限定宽度，则隐藏超出的内容）
            white-space: nowrap; //（设置文字在一行显示，不能换行）
            text-overflow: ellipsis; //（规定当文本溢出时，显示省略符号来代表被修剪的文本）

            .item {
                margin-left: 10px;
            }
        }
    }
}
</style>